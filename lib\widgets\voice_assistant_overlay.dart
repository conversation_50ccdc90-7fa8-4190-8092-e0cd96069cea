import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/conversation/bloc/conversation_bloc.dart';
import 'package:phoenix/features/conversation/bloc/conversation_event.dart';
import 'package:phoenix/features/conversation/bloc/conversation_state.dart';
import 'package:phoenix/features/conversation/widgets/conversation_message_widget.dart';
import 'package:phoenix/features/conversation/widgets/voice_input_widget.dart';
import 'package:phoenix/services/speech_recognition_service.dart';
import 'package:phoenix/services/voice_assistant_service.dart';

/// A conversational voice assistant overlay with persistent chat interface.
/// - Stays open until manually closed (no auto-close)
/// - Shows conversation history with user commands and AI responses
/// - Supports multiple voice interactions in one session
/// - Uses BLoC architecture for state management
/// - Speaks responses using text-to-speech
class VoiceAssistantOverlay extends StatelessWidget {
  const VoiceAssistantOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ConversationBloc(
        speechService: SpeechRecognitionService(),
        voiceService: VoiceAssistantService(),
      )..add(const ConversationStarted()),
      child: const _VoiceAssistantOverlayView(),
    );
  }
}

class _VoiceAssistantOverlayView extends StatefulWidget {
  const _VoiceAssistantOverlayView();

  @override
  State<_VoiceAssistantOverlayView> createState() => _VoiceAssistantOverlayViewState();
}

class _VoiceAssistantOverlayViewState extends State<_VoiceAssistantOverlayView>
    with TickerProviderStateMixin {
  late final AnimationController _pulseController;
  late final AnimationController _barsController;
  late final ScrollController _scrollController;
  
  bool _showScrollToBottom = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat();

    _barsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    final showButton = _scrollController.hasClients &&
        _scrollController.offset > 100;
    if (showButton != _showScrollToBottom) {
      setState(() {
        _showScrollToBottom = showButton;
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _autoScrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _barsController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConversationBloc, ConversationState>(
      listener: (context, state) {
        // Auto-scroll to bottom when new messages are added
        if (state.hasMessages) {
          _autoScrollToBottom();
        }
      },
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.9),
                Colors.indigo.withOpacity(0.8),
                Colors.black.withOpacity(0.9),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: SafeArea(
            child: Stack(
              children: [
                // Background particles/stars effect
                ...List.generate(20, (i) => _BackgroundParticle(
                  delay: i * 0.1,
                  controller: _pulseController,
                )),
                
                // Main content
                Column(
                  children: [
                    // Header
                    _buildHeader(context),
                    
                    // Conversation area
                    Expanded(
                      child: _buildConversationArea(),
                    ),
                    
                    // Voice input area
                    _buildVoiceInputArea(),
                    
                    // Action buttons
                    _buildActionButtons(context),
                  ],
                ),

                // Scroll to bottom button
                if (_showScrollToBottom)
                  Positioned(
                    bottom: 120,
                    right: 16,
                    child: FloatingActionButton(
                      mini: true,
                      onPressed: _scrollToBottom,
                      backgroundColor: Colors.blue.withOpacity(0.8),
                      child: const Icon(Icons.keyboard_arrow_down, color: Colors.white),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.purple.withOpacity(0.2),
              border: Border.all(color: Colors.purple.withOpacity(0.3)),
            ),
            child: Icon(
              Icons.psychology,
              color: Colors.purple.shade300,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Voice Assistant',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                BlocBuilder<ConversationBloc, ConversationState>(
                  builder: (context, state) {
                    return Text(
                      state.statusText,
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.1),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: IconButton(
              tooltip: 'Close',
              icon: const Icon(Icons.close, color: Colors.white70, size: 20),
              onPressed: () {
                context.read<ConversationBloc>().add(const ConversationClosed());
                Navigator.of(context).maybePop();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationArea() {
    return BlocBuilder<ConversationBloc, ConversationState>(
      builder: (context, state) {
        if (!state.hasMessages) {
          return _buildEmptyState();
        }
        debugPrint('👽ConversationBloc: Building conversation area with ${state.messages.length} messages');

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: state.messages.length,
          itemBuilder: (context, index) {
            final message = state.messages[index];
            return ConversationMessageWidget(
              message: message,
              showTimestamp: index == state.messages.length - 1 ||
                  (index < state.messages.length - 1 &&
                      state.messages[index + 1].timestamp
                          .difference(message.timestamp)
                          .inMinutes > 5),
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildPulsingMic(),
          const SizedBox(height: 32),
          _buildAnimatedBars(),
          const SizedBox(height: 32),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 32),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Column(
              children: [
                Text(
                  'Welcome to your Voice Assistant',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Start a conversation by tapping the microphone button below',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceInputArea() {
    return BlocBuilder<ConversationBloc, ConversationState>(
      builder: (context, state) {
        return VoiceInputWidget(
          partialText: state.currentInputDisplay,
          isListening: state.isListening,
          isPaused: state.isPaused,
          isFinalizing: state.isFinalizingInput,
          isProcessing: state.isProcessing,
          statusText: state.isInVoiceSession || state.isProcessing ? state.statusText : null,
          onStopListening: () {
            context.read<ConversationBloc>().add(const VoiceListeningStopped());
          },
        );
      },
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return BlocBuilder<ConversationBloc, ConversationState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Microphone button
              _buildMicrophoneButton(context, state),
              
              // Clear history button
              if (state.hasMessages)
                _buildClearButton(context),
              
              // Settings/mute button
              _buildSettingsButton(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMicrophoneButton(BuildContext context, ConversationState state) {
    final canListen = state.canListen;
    final isListening = state.isListening;
    
    return GestureDetector(
      onTap: canListen
          ? () {
              if (isListening) {
                context.read<ConversationBloc>().add(const VoiceListeningStopped());
              } else {
                context.read<ConversationBloc>().add(const VoiceListeningStarted());
              }
            }
          : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: isListening
                ? [Colors.red.shade400, Colors.red.shade600]
                : canListen
                    ? [Colors.blue.shade400, Colors.purple.shade400]
                    : [Colors.grey.shade400, Colors.grey.shade600],
          ),
          boxShadow: [
            BoxShadow(
              color: (isListening ? Colors.red : Colors.blue).withOpacity(0.4),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Icon(
          isListening ? Icons.stop : Icons.mic,
          color: Colors.white,
          size: 28,
        ),
      ),
    );
  }

  Widget _buildClearButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.1),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: IconButton(
        onPressed: () {
          context.read<ConversationBloc>().add(const ConversationHistoryCleared());
        },
        icon: const Icon(Icons.clear, color: Colors.white70),
        tooltip: 'Clear conversation',
      ),
    );
  }

  Widget _buildSettingsButton(BuildContext context, ConversationState state) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withOpacity(0.1),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: IconButton(
        onPressed: () {
          context.read<ConversationBloc>().add(const MicrophoneToggled());
        },
        icon: Icon(
          state.isMicrophoneMuted ? Icons.mic_off : Icons.settings_voice,
          color: state.isMicrophoneMuted ? Colors.red.shade300 : Colors.white70,
        ),
        tooltip: state.isMicrophoneMuted ? 'Unmute microphone' : 'Mute microphone',
      ),
    );
  }

  Widget _buildPulsingMic() {
    return SizedBox(
      width: 280,
      height: 280,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Outer glow effect
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  Colors.blue.withOpacity(0.1),
                  Colors.purple.withOpacity(0.05),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
            ),
          ),
          
          // Animated voice waves - multiple layers
          _AnimatedVoiceWave(controller: _barsController, radius: 120, color: Colors.blue.withOpacity(0.3), frequency: 1.0, amplitude: 0.8),
          _AnimatedVoiceWave(controller: _barsController, radius: 100, color: Colors.purple.withOpacity(0.4), frequency: 1.5, amplitude: 0.6),
          _AnimatedVoiceWave(controller: _barsController, radius: 80, color: Colors.cyan.withOpacity(0.5), frequency: 2.0, amplitude: 0.4),
          
          // Central AI orb
          BlocBuilder<ConversationBloc, ConversationState>(
            builder: (context, state) {
              return _AIOrb(controller: _pulseController, processing: state.isProcessing);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBars() {
    // Modern AI voice visualization
    return SizedBox(
      height: 60,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(7, (i) {
          final t = i / 7.0;
          return AnimatedBuilder(
            animation: _barsController,
            builder: (_, __) {
              final phase = (_barsController.value + t) % 1.0;
              final intensity = (math.sin(phase * math.pi * 4) + 1) / 2;
              final height = 8 + (intensity * 44);
              
              return Container(
                width: 4,
                height: height,
                margin: const EdgeInsets.symmetric(horizontal: 3),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.blue.withOpacity(0.8),
                      Colors.purple.withOpacity(0.6),
                      Colors.cyan.withOpacity(0.4),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.3),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              );
            },
          );
        }),
      ),
    );
  }

}

/// AI-style animated voice wave ring
class _AnimatedVoiceWave extends StatelessWidget {
  final AnimationController controller;
  final double radius;
  final Color color;
  final double frequency;
  final double amplitude;

  const _AnimatedVoiceWave({
    required this.controller,
    required this.radius,
    required this.color,
    required this.frequency,
    required this.amplitude,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (_, __) {
        return CustomPaint(
          size: Size(radius * 2, radius * 2),
          painter: _VoiceWavePainter(
            progress: controller.value,
            color: color,
            frequency: frequency,
            amplitude: amplitude,
          ),
        );
      },
    );
  }
}

/// Central AI orb with breathing animation
class _AIOrb extends StatelessWidget {
  final AnimationController controller;
  final bool processing;

  const _AIOrb({required this.controller, required this.processing});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (_, __) {
        final breathScale = 1.0 + (math.sin(controller.value * math.pi * 2) * 0.1);
        return Transform.scale(
          scale: breathScale,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: processing 
                  ? [
                      Colors.orange.withOpacity(0.8),
                      Colors.deepOrange.withOpacity(0.6),
                      Colors.red.withOpacity(0.4),
                    ]
                  : [
                      Colors.blue.withOpacity(0.8),
                      Colors.purple.withOpacity(0.6),
                      Colors.cyan.withOpacity(0.4),
                    ],
              ),
              boxShadow: [
                BoxShadow(
                  color: processing ? Colors.orange.withOpacity(0.4) : Colors.blue.withOpacity(0.4),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              processing ? Icons.psychology : Icons.mic,
              color: Colors.white,
              size: 32,
            ),
          ),
        );
      },
    );
  }
}

/// Custom painter for voice wave visualization
class _VoiceWavePainter extends CustomPainter {
  final double progress;
  final Color color;
  final double frequency;
  final double amplitude;

  _VoiceWavePainter({
    required this.progress,
    required this.color,
    required this.frequency,
    required this.amplitude,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final center = Offset(size.width / 2, size.height / 2);
    final baseRadius = size.width / 4;

    final path = Path();
    const int points = 60;
    
    for (int i = 0; i <= points; i++) {
      final angle = (i / points) * 2 * math.pi;
      final waveOffset = math.sin(progress * frequency * 2 * math.pi + angle * 4) * amplitude * 10;
      final radius = baseRadius + waveOffset;
      
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Background floating particles for ambiance
class _BackgroundParticle extends StatelessWidget {
  final double delay;
  final AnimationController controller;

  const _BackgroundParticle({required this.delay, required this.controller});

  @override
  Widget build(BuildContext context) {
    final random = math.Random((delay * 1000).toInt());
    final screenSize = MediaQuery.of(context).size;
    
    return AnimatedBuilder(
      animation: controller,
      builder: (_, __) {
        final progress = (controller.value + delay) % 1.0;
        final opacity = (math.sin(progress * math.pi) * 0.5).clamp(0.0, 0.5);
        final scale = 0.5 + (math.sin(progress * math.pi * 2) * 0.3);
        
        return Positioned(
          left: random.nextDouble() * screenSize.width,
          top: random.nextDouble() * screenSize.height,
          child: Transform.scale(
            scale: scale,
            child: Container(
              width: 2 + random.nextDouble() * 4,
              height: 2 + random.nextDouble() * 4,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withOpacity(opacity),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.2),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}